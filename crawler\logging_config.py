"""
Crawler-Specific Logging Configuration

This module extends the unified logging configuration from app.logging_config
with crawler-specific loggers, context variables, and formatting patterns.

Crawler Logging Architecture:
- Core Layer: Web scraping operations, browser automation
- Server Layer: API endpoints, business logic
- Presentation Layer: Streamlit UI, user interactions
- Client Layer: CLI operations, external API calls
"""

import logging
import os
from typing import Dict, Any, Optional
from contextvars import ContextVar

from app.logging_config import (
    setup_logging as base_setup_logging,
    set_layer_context, set_operation_context,
    get_layer_context, get_operation_context
)

# Crawler-specific context variables
crawler_session_id: ContextVar[Optional[str]] = ContextVar('crawler_session_id', default=None)
crawler_browser_id: ContextVar[Optional[str]] = ContextVar('crawler_browser_id', default=None)
crawler_page_url: ContextVar[Optional[str]] = ContextVar('crawler_page_url', default=None)
crawler_keyword: ContextVar[Optional[str]] = ContextVar('crawler_keyword', default=None)
crawler_login_status: ContextVar[Optional[str]] = ContextVar('crawler_login_status', default=None)


class CrawlerContextFilter(logging.Filter):
    """Add crawler-specific context to log records"""
    
    def filter(self, record):
        # Add crawler context variables to log record
        record.crawler_session_id = crawler_session_id.get()
        record.crawler_browser_id = crawler_browser_id.get()
        record.crawler_page_url = crawler_page_url.get()
        record.crawler_keyword = crawler_keyword.get()
        record.crawler_login_status = crawler_login_status.get()
        
        # Add layer and operation context
        record.layer_context = get_layer_context()
        record.operation_context = get_operation_context()
        
        return True


class CrawlerFormatter(logging.Formatter):
    """Enhanced formatter for crawler operations"""
    
    def __init__(self):
        # Enhanced format with crawler context
        fmt = (
            "%(asctime)s | %(levelname)-8s | %(name)s | "
            "Layer:%(layer_context)s | Op:%(operation_context)s | "
            "Session:%(crawler_session_id)s | URL:%(crawler_page_url)s | "
            "Keyword:%(crawler_keyword)s | Login:%(crawler_login_status)s | "
            "%(message)s"
        )
        super().__init__(fmt, datefmt='%Y-%m-%d %H:%M:%S')
    
    def format(self, record):
        # Ensure all context fields exist with defaults
        for field in ['crawler_session_id', 'crawler_browser_id', 'crawler_page_url', 
                     'crawler_keyword', 'crawler_login_status', 'layer_context', 'operation_context']:
            if not hasattr(record, field):
                setattr(record, field, 'N/A')
        
        # Truncate long URLs for readability
        if hasattr(record, 'crawler_page_url') and record.crawler_page_url:
            if len(record.crawler_page_url) > 50:
                record.crawler_page_url = record.crawler_page_url[:47] + "..."
        
        return super().format(record)


def setup_crawler_logging():
    """Setup crawler-specific logging configuration"""
    # First setup base logging
    base_setup_logging()

    # Get crawler-specific log level
    crawler_log_level = os.getenv("CRAWLER_LOG_LEVEL", "INFO").upper()

    # Setup crawler-specific loggers - they will use the main crawler.log file
    # through the unified logging configuration
    crawler_loggers = [
        'crawler',
        'crawler.core',
        'crawler.server',
        'crawler.streamlit_app',
        'crawler.client',
        'crawler.session_manager',
        'crawler.utilities'
    ]

    for logger_name in crawler_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, crawler_log_level))

        # Add crawler context filter to existing handlers
        crawler_filter = CrawlerContextFilter()
        for handler in logger.handlers:
            if not any(isinstance(f, CrawlerContextFilter) for f in handler.filters):
                handler.addFilter(crawler_filter)


def set_crawler_session_context(session_id: str):
    """Set crawler session ID context"""
    crawler_session_id.set(session_id)


def set_crawler_browser_context(browser_id: str):
    """Set crawler browser ID context"""
    crawler_browser_id.set(browser_id)


def set_crawler_page_context(page_url: str):
    """Set current page URL context"""
    crawler_page_url.set(page_url)


def set_crawler_keyword_context(keyword: str):
    """Set search keyword context"""
    crawler_keyword.set(keyword)


def set_crawler_login_context(login_status: str):
    """Set login status context"""
    crawler_login_status.set(login_status)


def get_crawler_context() -> Dict[str, Any]:
    """Get all crawler context variables"""
    return {
        'session_id': crawler_session_id.get(),
        'browser_id': crawler_browser_id.get(),
        'page_url': crawler_page_url.get(),
        'keyword': crawler_keyword.get(),
        'login_status': crawler_login_status.get(),
        'layer_context': get_layer_context(),
        'operation_context': get_operation_context()
    }


def clear_crawler_context():
    """Clear all crawler context variables"""
    crawler_session_id.set(None)
    crawler_browser_id.set(None)
    crawler_page_url.set(None)
    crawler_keyword.set(None)
    crawler_login_status.set(None)


class CrawlerLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter that automatically includes crawler context"""
    
    def __init__(self, logger, extra=None):
        super().__init__(logger, extra or {})
    
    def process(self, msg, kwargs):
        # Add crawler context to extra
        crawler_context = get_crawler_context()
        if 'extra' not in kwargs:
            kwargs['extra'] = {}
        kwargs['extra'].update(crawler_context)
        
        return msg, kwargs


def get_crawler_logger(name: str) -> CrawlerLoggerAdapter:
    """Get a crawler logger with automatic context inclusion"""
    base_logger = logging.getLogger(name)
    return CrawlerLoggerAdapter(base_logger)


# Convenience loggers for different crawler components
core_logger = get_crawler_logger('crawler.core')
server_logger = get_crawler_logger('crawler.server')
ui_logger = get_crawler_logger('crawler.streamlit_app')
client_logger = get_crawler_logger('crawler.client')
session_logger = get_crawler_logger('crawler.session_manager')
utils_logger = get_crawler_logger('crawler.utilities')


# Performance monitoring helpers
def log_operation_timing(logger, operation_name: str):
    """Decorator to log operation timing"""
    import time
    from functools import wraps
    
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Operation '{operation_name}' completed in {duration:.2f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Operation '{operation_name}' failed after {duration:.2f}s: {str(e)}")
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Operation '{operation_name}' completed in {duration:.2f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Operation '{operation_name}' failed after {duration:.2f}s: {str(e)}")
                raise
        
        return async_wrapper if hasattr(func, '__code__') and func.__code__.co_flags & 0x80 else sync_wrapper
    
    return decorator


def log_browser_action(action: str, details: str = None):
    """Log browser actions with context"""
    logger = get_crawler_logger('crawler.browser_actions')
    context = get_crawler_context()
    
    log_msg = f"Browser Action: {action}"
    if details:
        log_msg += f" | Details: {details}"
    
    logger.info(log_msg, extra=context)


def log_search_operation(keyword: str, result_count: int = None, success: bool = True):
    """Log search operations with context"""
    logger = get_crawler_logger('crawler.search_operations')
    
    # Set keyword context
    set_crawler_keyword_context(keyword)
    
    if success:
        msg = f"Search completed for keyword: {keyword}"
        if result_count is not None:
            msg += f" | Results: {result_count}"
        logger.info(msg)
    else:
        logger.error(f"Search failed for keyword: {keyword}")


def log_login_attempt(status: str, details: str = None):
    """Log login attempts with context"""
    logger = get_crawler_logger('crawler.login_operations')
    
    # Set login status context
    set_crawler_login_context(status)
    
    log_msg = f"Login attempt: {status}"
    if details:
        log_msg += f" | Details: {details}"
    
    if status in ['success', 'already_logged_in']:
        logger.info(log_msg)
    elif status in ['qr_code_required', 'pending']:
        logger.warning(log_msg)
    else:
        logger.error(log_msg)


# Initialize crawler logging on module import
setup_crawler_logging()

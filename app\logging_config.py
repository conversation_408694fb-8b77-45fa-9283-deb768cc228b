import os
import logging
import logging.config
from pathlib import Path
import re
import json
from typing import Dict, Any, Optional
from contextvars import Con<PERSON><PERSON><PERSON>
import uuid

# Context variables for enhanced logging
request_id_var: ContextVar[str] = ContextVar('request_id', default=None)
layer_context_var: ContextVar[str] = ContextVar('layer_context', default=None)
operation_context_var: ContextVar[str] = ContextVar('operation_context', default=None)

class UTF8JsonFormatter(logging.Formatter):
    """Custom JSON formatter that ensures UTF-8 encoding and includes error handling context."""

    def format(self, record):
        """Format the log record as JSON."""
        log_record = {
            'asctime': self.formatTime(record),
            'level': record.levelname,
            'name': record.name,
            'process': record.process,
            'thread': record.thread,
            'module': record.module,
            'funcName': record.funcName,
            'pathname': record.pathname,
            'lineno': record.lineno,
            'message': record.getMessage(),
        }

        # Add request ID and context if available
        if hasattr(record, 'request_id') and record.request_id:
            log_record['request_id'] = record.request_id
        if hasattr(record, 'layer_context') and record.layer_context:
            log_record['layer'] = record.layer_context
        if hasattr(record, 'operation_context') and record.operation_context:
            log_record['operation'] = record.operation_context

        # Add exception context if available
        if hasattr(record, 'error_code') and record.error_code:
            log_record['error_code'] = record.error_code
        if hasattr(record, 'error_layer') and record.error_layer:
            log_record['error_layer'] = record.error_layer
        if hasattr(record, 'error_severity') and record.error_severity:
            log_record['error_severity'] = record.error_severity
        if hasattr(record, 'error_context') and record.error_context:
            log_record['error_context'] = record.error_context
        if hasattr(record, 'suggested_action') and record.suggested_action:
            log_record['suggested_action'] = record.suggested_action
        if hasattr(record, 'original_exception_type') and record.original_exception_type:
            log_record['original_exception_type'] = record.original_exception_type

        # Add exception info if present
        if record.exc_info:
            log_record['exc_info'] = self.formatException(record.exc_info)

        return json.dumps(log_record, ensure_ascii=False, default=str)

class RequestIdFilter(logging.Filter):
    """Injects the request_id and layer context into the log record."""
    def filter(self, record: logging.LogRecord) -> bool:
        record.request_id = request_id_var.get()
        record.layer_context = layer_context_var.get()
        record.operation_context = operation_context_var.get()
        return True


class ExceptionContextFilter(logging.Filter):
    """Enhances log records with exception context from unified error handling."""

    def filter(self, record: logging.LogRecord) -> bool:
        # Check if this is an exception record with our custom exception
        if hasattr(record, 'exc_info') and record.exc_info:
            exc_type, exc_value, exc_traceback = record.exc_info

            # If it's one of our custom exceptions, extract additional context
            if hasattr(exc_value, 'get_full_context'):
                try:
                    context = exc_value.get_full_context()
                    # Add exception context to log record
                    record.error_code = context.get('error_code')
                    record.error_layer = context.get('layer')
                    record.error_severity = context.get('severity')
                    record.error_context = context.get('context', {})
                    record.suggested_action = context.get('suggested_action')
                    record.original_exception_type = context.get('original_exception_type')
                except Exception:
                    # Don't let filter errors break logging
                    pass

        return True


class LayerSpecificFilter(logging.Filter):
    """Filter logs based on application layer."""

    def __init__(self, allowed_layers=None):
        super().__init__()
        self.allowed_layers = allowed_layers or []

    def filter(self, record: logging.LogRecord) -> bool:
        if not self.allowed_layers:
            return True

        layer_context = getattr(record, 'layer_context', None)
        if layer_context:
            return layer_context in self.allowed_layers

        # Check module name for layer classification
        module_name = getattr(record, 'name', '')  # Use 'name' instead of 'module'
        if 'api' in module_name or 'main' in module_name or 'cli' in module_name:
            return 'presentation' in self.allowed_layers
        elif 'agents' in module_name or 'workflows' in module_name:
            return 'business_logic' in self.allowed_layers
        elif 'utils' in module_name:
            return 'utility' in self.allowed_layers

        return True

class SensitiveDataFilter(logging.Filter):
    """Filter to mask sensitive data in logs."""
    def __init__(self):
        super().__init__()
        self.patterns = [
            re.compile(r'\b(sk|pk|rk)-[a-zA-Z0-9-]{20,}\b', re.IGNORECASE),  # More robust API key pattern
        ]

    def mask(self, message: str) -> str:
        # Ensure message is a string before masking
        message = str(message)
        for pattern in self.patterns:
            message = pattern.sub('***MASKED***', message)
        return message

    def filter(self, record: logging.LogRecord) -> bool:
        if isinstance(record.msg, str):
            record.msg = self.mask(record.msg)
        if record.args:
            new_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    new_args.append(self.mask(arg))
                else:
                    new_args.append(arg)
            record.args = tuple(new_args)
        return True


class ComponentSpecificFilter(logging.Filter):
    """Filter logs based on application component (streamlit, server, crawler, cli)."""

    def __init__(self, allowed_components=None):
        super().__init__()
        self.allowed_components = allowed_components or []

    def filter(self, record: logging.LogRecord) -> bool:
        if not self.allowed_components:
            return True

        # Check layer context first
        layer_context = getattr(record, 'layer_context', None)
        if layer_context:
            # Map layer contexts to components
            if layer_context == 'presentation' and 'streamlit' in self.allowed_components:
                return True
            elif layer_context in ['server', 'api'] and any(comp in self.allowed_components for comp in ['server', 'api']):
                return True
            elif layer_context == 'cli' and 'cli' in self.allowed_components:
                return True

        # Check module name for component classification
        module_name = getattr(record, 'name', '')
        pathname = getattr(record, 'pathname', '')

        # Streamlit component detection
        if 'streamlit' in self.allowed_components:
            if ('main.py' in pathname and 'app' in pathname) or 'streamlit' in module_name.lower():
                return True

        # Server component detection
        if any(comp in self.allowed_components for comp in ['server', 'api']):
            if 'server.py' in pathname or 'api' in module_name or 'uvicorn' in module_name:
                return True

        # Crawler component detection
        if 'crawler' in self.allowed_components:
            if 'crawler' in module_name or 'crawler' in pathname:
                return True

        # CLI component detection
        if 'cli' in self.allowed_components:
            if 'cli' in module_name or 'cli' in pathname or layer_context == 'cli':
                return True

        return False

def setup_logging():
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_to_file = os.getenv('LOG_TO_FILE', 'true').lower() == 'true'

    # Create logs directory if it doesn't exist
    logs_dir = Path(__file__).resolve().parent.parent / 'logs'
    logs_dir.mkdir(exist_ok=True)

    log_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'filters': {
            'sensitive_data_filter': {
                '()': SensitiveDataFilter,
            },
            'request_id_filter': {
                '()': RequestIdFilter,
            },
            'exception_context_filter': {
                '()': ExceptionContextFilter,
            },
            'streamlit_filter': {
                '()': ComponentSpecificFilter,
                'allowed_components': ['streamlit']
            },
            'server_filter': {
                '()': ComponentSpecificFilter,
                'allowed_components': ['server', 'api']
            },
            'crawler_filter': {
                '()': ComponentSpecificFilter,
                'allowed_components': ['crawler']
            },
            'cli_filter': {
                '()': ComponentSpecificFilter,
                'allowed_components': ['cli']
            }
        },
        'formatters': {
            'verbose': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s,%(layer_context)s,%(operation_context)s,%(thread)d,%(funcName)s,%(lineno)d] - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'json': {
                '()': UTF8JsonFormatter,
                'format': '''
                    asctime: %(asctime)s.%(msecs)03d
                    level: %(levelname)s
                    request_id: %(request_id)s
                    process: %(process)d
                    thread: %(thread)d
                    module: %(module)s
                    funcName: %(funcName)s
                    pathname: %(pathname)s
                    lineno: %(lineno)d
                    message: %(message)s
                ''',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'filters': ['sensitive_data_filter', 'request_id_filter'],
                'formatter': 'verbose',
                'stream': 'ext://sys.stdout',
            },
        },
        'loggers': {
            '': {  # root logger
                'handlers': ['console'],
                'level': log_level,
            },
            # Streamlit UI Application Logger
            'app': {
                'handlers': ['console', 'streamlit_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.main': {
                'handlers': ['console', 'streamlit_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # Server/API Component Logger
            'app.api': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.api.server': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'uvicorn': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'INFO',
                'propagate': False,
            },
            'uvicorn.access': {
                'handlers': ['server_file'] if log_to_file else [],
                'level': 'INFO',
                'propagate': False,
            },
            'uvicorn.error': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'WARNING',
                'propagate': False,
            },
            # Crawler Component Logger
            'crawler': {
                'handlers': ['console', 'crawler_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'crawler.server': {
                'handlers': ['console', 'crawler_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'crawler.core': {
                'handlers': ['console', 'crawler_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'crawler.streamlit_app': {
                'handlers': ['console', 'crawler_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # CLI Component Logger
            'app.cli': {
                'handlers': ['console', 'cli_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            '__main__': {
                'handlers': ['console', 'cli_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # Business Logic and Utility Loggers (route to server)
            'app.agents': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.workflows': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.utils': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # External libraries
            'httpx': {
                'level': 'WARNING',
                'propagate': True,
            }
        }
    }
    
    # Add file handlers if enabled - 4 dedicated log files
    if log_to_file:
        # 1. Streamlit UI Application logs
        log_config['handlers']['streamlit_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'streamlit.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'streamlit_filter'],
            'formatter': 'json',
        }

        # 2. Server/API Component logs
        log_config['handlers']['server_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'server.log',
            'maxBytes': 100 * 1024 * 1024,  # 100MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'server_filter'],
            'formatter': 'json',
        }

        # 3. Crawler Component logs
        log_config['handlers']['crawler_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'crawler.log',
            'maxBytes': 100 * 1024 * 1024,  # 100MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'crawler_filter'],
            'formatter': 'json',
        }

        # 4. CLI Component logs
        log_config['handlers']['cli_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'cli.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'cli_filter'],
            'formatter': 'json',
        }

    logging.config.dictConfig(log_config)
    
    # Set more granular log levels
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('openai').setLevel(logging.INFO)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # Add trace logging for debugging
    logging.getLogger('app.workflows').setLevel('DEBUG')
    logging.getLogger('app.api').setLevel('DEBUG')


# Utility functions for setting logging context
def set_layer_context(layer: str):
    """Set the current layer context for logging."""
    layer_context_var.set(layer)


def set_operation_context(operation: str):
    """Set the current operation context for logging."""
    operation_context_var.set(operation)


def get_layer_context() -> Optional[str]:
    """Get the current layer context for logging."""
    return layer_context_var.get()


def get_operation_context() -> Optional[str]:
    """Get the current operation context for logging."""
    return operation_context_var.get()


def clear_logging_context():
    """Clear all logging context variables."""
    layer_context_var.set(None)
    operation_context_var.set(None)


def get_layer_logger(layer: str, module_name: str = None):
    """Get a logger configured for a specific layer."""
    if module_name:
        logger_name = f"app.{module_name}"
    else:
        if layer == 'presentation':
            logger_name = 'app.api'
        elif layer == 'business_logic':
            logger_name = 'app.workflows'
        elif layer == 'utility':
            logger_name = 'app.utils'
        else:
            logger_name = 'app'

    logger = logging.getLogger(logger_name)
    set_layer_context(layer)
    return logger


def get_component_logger(component: str, module_name: str = None):
    """Get a logger configured for a specific component (streamlit, server, crawler, cli)."""
    if module_name:
        logger_name = module_name
    else:
        if component == 'streamlit':
            logger_name = 'app'
        elif component == 'server':
            logger_name = 'app.api'
        elif component == 'crawler':
            logger_name = 'crawler'
        elif component == 'cli':
            logger_name = 'app.cli'
        else:
            logger_name = 'app'

    logger = logging.getLogger(logger_name)
    # Set appropriate layer context based on component
    if component == 'streamlit':
        set_layer_context('presentation')
    elif component == 'server':
        set_layer_context('server')
    elif component == 'crawler':
        set_layer_context('crawler')
    elif component == 'cli':
        set_layer_context('cli')

    return logger


if __name__ == '__main__':
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Logging setup complete.")
    logger.debug("This is a debug message.")
    logger.warning("This is a warning.")
    logger.error("This is an error with sensitive data: api_key=12345")
    logger.info("Another message.")
